use crate::app_config::Config;
use crate::tracing_config::TracingParams;
use std::sync::Arc;
use tokio::sync::{RwLock, broadcast};
use std::path::PathBuf;
use std::time::{SystemTime, Duration};
use tracing::{info, warn, error};
use tokio::fs;
use tokio::time::interval;
use serde::{Deserialize, Serialize};

/// Configuration change event types
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum ConfigChangeType {
    RateLimiting,
    HealthCheck,
    Logging,
    Authentication,
    Performance,
    Full, // Complete configuration reload
}

/// Configuration change notification
#[derive(Debug, <PERSON>lone)]
pub struct ConfigChange {
    pub change_type: ConfigChangeType,
    //pub timestamp: SystemTime,
    //pub config_version: u64,
}

/// Hot-reloadable configuration subset
#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct HotReloadableConfig {
    // Rate limiting settings
    pub rate_limiting_enabled: bool,
    pub rate_limit_type: String,
    pub rate_limit_requests_per_second: u32,
    pub rate_limit_burst_size: u32,
    pub rate_limit_global_requests_per_second: u32,
    pub rate_limit_global_burst_size: u32,
    pub rate_limit_cleanup_interval_secs: u64,
    
    // Health check settings
    pub redis_keepalive_enabled: bool,
    pub redis_keepalive_interval_secs: u64,
    pub redis_idle_threshold_secs: u64,
    pub secondary_nodes_keepalive_enabled: bool,
    pub secondary_nodes_keepalive_interval_secs: u64,
    pub secondary_nodes_idle_threshold_secs: u64,
    pub site_nodes_keepalive_enabled: bool,
    pub site_nodes_keepalive_interval_secs: u64,
    pub site_nodes_idle_threshold_secs: u64,
    pub peer_redis_keepalive_enabled: bool,
    pub peer_redis_keepalive_interval_secs: u64,
    pub peer_redis_idle_threshold_secs: u64,
    
    // Authentication settings
    pub auth_enabled: bool,
    pub auth_mode: String,
    pub auth_token_expiry_enabled: bool,
    pub session_duration_secs: u64,
    
    // Performance tuning settings
    pub max_retries: u32,
    pub retry_delay_ms: u64,
    pub batch_flush_interval_ms: u64,
    pub max_batch_size: usize,
    pub replication_batch_max_age_secs: u64,

    // Hot configuration reload settings
    pub config_watch_interval_secs: u64,
}

impl From<&Config> for HotReloadableConfig {
    fn from(config: &Config) -> Self {
        Self {
            rate_limiting_enabled: config.rate_limiting_enabled,
            rate_limit_type: config.rate_limit_type.clone(),
            rate_limit_requests_per_second: config.rate_limit_requests_per_second,
            rate_limit_burst_size: config.rate_limit_burst_size,
            rate_limit_global_requests_per_second: config.rate_limit_global_requests_per_second,
            rate_limit_global_burst_size: config.rate_limit_global_burst_size,
            rate_limit_cleanup_interval_secs: config.rate_limit_cleanup_interval_secs,
            
            redis_keepalive_enabled: config.redis_keepalive_enabled,
            redis_keepalive_interval_secs: config.redis_keepalive_interval_secs,
            redis_idle_threshold_secs: config.redis_idle_threshold_secs,
            secondary_nodes_keepalive_enabled: config.secondary_nodes_keepalive_enabled,
            secondary_nodes_keepalive_interval_secs: config.secondary_nodes_keepalive_interval_secs,
            secondary_nodes_idle_threshold_secs: config.secondary_nodes_idle_threshold_secs,
            site_nodes_keepalive_enabled: config.site_nodes_keepalive_enabled,
            site_nodes_keepalive_interval_secs: config.site_nodes_keepalive_interval_secs,
            site_nodes_idle_threshold_secs: config.site_nodes_idle_threshold_secs,
            peer_redis_keepalive_enabled: config.peer_redis_keepalive_enabled,
            peer_redis_keepalive_interval_secs: config.peer_redis_keepalive_interval_secs,
            peer_redis_idle_threshold_secs: config.peer_redis_idle_threshold_secs,
            
            auth_enabled: config.auth_enabled,
            auth_mode: config.auth_mode.clone(),
            auth_token_expiry_enabled: config.auth_token_expiry_enabled,
            session_duration_secs: config.session_duration_secs,
            
            max_retries: config.max_retries,
            retry_delay_ms: config.retry_delay_ms,
            batch_flush_interval_ms: config.batch_flush_interval_ms,
            max_batch_size: config.max_batch_size,
            replication_batch_max_age_secs: config.replication_batch_max_age_secs,

            config_watch_interval_secs: config.config_watch_interval_secs,
        }
    }
}

/// Configuration manager for hot reloading
pub struct ConfigManager {
    config_file_path: PathBuf,
    log_config_file_path: PathBuf,
    current_config: Arc<RwLock<Config>>,
    current_hot_config: Arc<RwLock<HotReloadableConfig>>,
    current_log_config: Arc<RwLock<TracingParams>>,
    config_version: Arc<RwLock<u64>>,
    last_modified: Arc<RwLock<SystemTime>>,
    log_last_modified: Arc<RwLock<SystemTime>>,
    change_sender: broadcast::Sender<ConfigChange>,
    _change_receiver: broadcast::Receiver<ConfigChange>,
    watch_interval: Arc<RwLock<Duration>>,
}

impl ConfigManager {
    /// Create a new configuration manager
    pub fn new(
        config_file_path: &str,
        log_config_file_path: &str,
        initial_config: Config,
        initial_log_config: TracingParams,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let config_path = PathBuf::from(config_file_path);
        let log_config_path = PathBuf::from(log_config_file_path);
        
        // Get initial file modification times
        let config_modified = std::fs::metadata(&config_path)
            .map(|m| m.modified().unwrap_or(SystemTime::UNIX_EPOCH))
            .unwrap_or(SystemTime::UNIX_EPOCH);
            
        let log_config_modified = std::fs::metadata(&log_config_path)
            .map(|m| m.modified().unwrap_or(SystemTime::UNIX_EPOCH))
            .unwrap_or(SystemTime::UNIX_EPOCH);
        
        let hot_config = HotReloadableConfig::from(&initial_config);
        
        let (change_sender, change_receiver) = broadcast::channel(100);
        
        let watch_interval_secs = initial_config.config_watch_interval_secs;

        Ok(Self {
            config_file_path: config_path,
            log_config_file_path: log_config_path,
            current_config: Arc::new(RwLock::new(initial_config)),
            current_hot_config: Arc::new(RwLock::new(hot_config)),
            current_log_config: Arc::new(RwLock::new(initial_log_config)),
            config_version: Arc::new(RwLock::new(1)),
            last_modified: Arc::new(RwLock::new(config_modified)),
            log_last_modified: Arc::new(RwLock::new(log_config_modified)),
            change_sender,
            _change_receiver: change_receiver,
            watch_interval: Arc::new(RwLock::new(Duration::from_secs(watch_interval_secs))),
        })
    }
    
    /// Get the current hot-reloadable configuration
    pub async fn get_hot_config(&self) -> HotReloadableConfig {
        self.current_hot_config.read().await.clone()
    }
    
    /// Subscribe to configuration changes
    pub fn subscribe_to_changes(&self) -> broadcast::Receiver<ConfigChange> {
        self.change_sender.subscribe()
    }
    
    /// Start the configuration file watcher
    pub async fn start_file_watcher(self: Arc<Self>) {
        let initial_interval = *self.watch_interval.read().await;
        info!("Starting configuration file watcher with interval: {:?}", initial_interval);

        let mut interval_timer = interval(initial_interval);
        let mut current_interval = initial_interval;

        loop {
            interval_timer.tick().await;

            // Check if watch interval has changed
            let new_interval = *self.watch_interval.read().await;
            if new_interval != current_interval {
                info!("Configuration watch interval changed from {:?} to {:?}", current_interval, new_interval);
                interval_timer = interval(new_interval);
                current_interval = new_interval;
            }

            // Check main configuration file
            if let Err(e) = self.check_config_file_changes().await {
                error!("Error checking configuration file changes: {}", e);
            }

            // Check log configuration file
            if let Err(e) = self.check_log_config_file_changes().await {
                error!("Error checking log configuration file changes: {}", e);
            }
        }
    }

    /// Check for changes in the main configuration file
    async fn check_config_file_changes(&self) -> Result<(), Box<dyn std::error::Error>> {
        let metadata = fs::metadata(&self.config_file_path).await?;
        let modified = metadata.modified()?;

        let last_modified = *self.last_modified.read().await;

        if modified > last_modified {
            info!("Configuration file changed, reloading: {:?}", self.config_file_path);

            // Update last modified time
            *self.last_modified.write().await = modified;

            // Reload configuration
            self.reload_config().await?;
        }

        Ok(())
    }

    /// Check for changes in the log configuration file
    async fn check_log_config_file_changes(&self) -> Result<(), Box<dyn std::error::Error>> {
        let metadata = fs::metadata(&self.log_config_file_path).await?;
        let modified = metadata.modified()?;

        let last_modified = *self.log_last_modified.read().await;

        if modified > last_modified {
            info!("Log configuration file changed, reloading: {:?}", self.log_config_file_path);

            // Update last modified time
            *self.log_last_modified.write().await = modified;

            // Reload log configuration
            self.reload_log_config().await?;
        }

        Ok(())
    }

    /// Reload the main configuration file
    async fn reload_config(&self) -> Result<(), Box<dyn std::error::Error>> {
        // Load new configuration
        let config_file_str = self.config_file_path.to_str()
            .ok_or("Invalid config file path")?;

        let new_config = Config::from_file(Some(config_file_str));
        let new_hot_config = HotReloadableConfig::from(&new_config);

        // Determine what changed
        let old_hot_config = self.current_hot_config.read().await.clone();
        let change_types = self.detect_config_changes(&old_hot_config, &new_hot_config);

        // Update configuration
        *self.current_config.write().await = new_config.clone();
        *self.current_hot_config.write().await = new_hot_config.clone();

        // Update watch interval if it changed
        if old_hot_config.config_watch_interval_secs != new_hot_config.config_watch_interval_secs {
            let new_interval = Duration::from_secs(new_hot_config.config_watch_interval_secs);
            *self.watch_interval.write().await = new_interval;
            info!("Configuration watch interval updated to: {:?}", new_interval);
        }

        // Increment version
        let mut version = self.config_version.write().await;
        *version += 1;
        let new_version = *version;
        drop(version);

        // Notify subscribers of changes
        for change_type in change_types {
            let change = ConfigChange {
                change_type,
                //timestamp: SystemTime::now(),
                //config_version: new_version,
            };

            if let Err(e) = self.change_sender.send(change.clone()) {
                warn!("Failed to send configuration change notification: {}", e);
            } else {
                info!("Configuration change notification sent: {:?}", change.change_type);
            }
        }

        info!("Configuration reloaded successfully, version: {}", new_version);
        Ok(())
    }

    /// Reload the log configuration file
    async fn reload_log_config(&self) -> Result<(), Box<dyn std::error::Error>> {
        let log_config_file_str = self.log_config_file_path.to_str()
            .ok_or("Invalid log config file path")?;

        let new_log_config = TracingParams::from_file(Some(log_config_file_str))?;

        // Update log configuration
        *self.current_log_config.write().await = new_log_config;

        // Increment version and notify
        let mut version = self.config_version.write().await;
        *version += 1;
        let new_version = *version;
        drop(version);

        let change = ConfigChange {
            change_type: ConfigChangeType::Logging,
            //timestamp: SystemTime::now(),
            //config_version: new_version,
        };

        if let Err(e) = self.change_sender.send(change.clone()) {
            warn!("Failed to send log configuration change notification: {}", e);
        } else {
            info!("Log configuration change notification sent");
        }

        info!("Log configuration reloaded successfully, version: {}", new_version);
        Ok(())
    }

    /// Detect what types of configuration have changed
    fn detect_config_changes(&self, old: &HotReloadableConfig, new: &HotReloadableConfig) -> Vec<ConfigChangeType> {
        let mut changes = Vec::new();

        // Check rate limiting changes
        if old.rate_limiting_enabled != new.rate_limiting_enabled ||
           old.rate_limit_type != new.rate_limit_type ||
           old.rate_limit_requests_per_second != new.rate_limit_requests_per_second ||
           old.rate_limit_burst_size != new.rate_limit_burst_size ||
           old.rate_limit_global_requests_per_second != new.rate_limit_global_requests_per_second ||
           old.rate_limit_global_burst_size != new.rate_limit_global_burst_size ||
           old.rate_limit_cleanup_interval_secs != new.rate_limit_cleanup_interval_secs {
            changes.push(ConfigChangeType::RateLimiting);
        }

        // Check health check changes
        if old.redis_keepalive_enabled != new.redis_keepalive_enabled ||
           old.redis_keepalive_interval_secs != new.redis_keepalive_interval_secs ||
           old.redis_idle_threshold_secs != new.redis_idle_threshold_secs ||
           old.secondary_nodes_keepalive_enabled != new.secondary_nodes_keepalive_enabled ||
           old.secondary_nodes_keepalive_interval_secs != new.secondary_nodes_keepalive_interval_secs ||
           old.secondary_nodes_idle_threshold_secs != new.secondary_nodes_idle_threshold_secs ||
           old.site_nodes_keepalive_enabled != new.site_nodes_keepalive_enabled ||
           old.site_nodes_keepalive_interval_secs != new.site_nodes_keepalive_interval_secs ||
           old.site_nodes_idle_threshold_secs != new.site_nodes_idle_threshold_secs ||
           old.peer_redis_keepalive_enabled != new.peer_redis_keepalive_enabled ||
           old.peer_redis_keepalive_interval_secs != new.peer_redis_keepalive_interval_secs ||
           old.peer_redis_idle_threshold_secs != new.peer_redis_idle_threshold_secs {
            changes.push(ConfigChangeType::HealthCheck);
        }

        // Check authentication changes
        if old.auth_enabled != new.auth_enabled ||
           old.auth_mode != new.auth_mode ||
           old.auth_token_expiry_enabled != new.auth_token_expiry_enabled ||
           old.session_duration_secs != new.session_duration_secs {
            changes.push(ConfigChangeType::Authentication);
        }

        // Check performance changes
        if old.max_retries != new.max_retries ||
           old.retry_delay_ms != new.retry_delay_ms ||
           old.batch_flush_interval_ms != new.batch_flush_interval_ms ||
           old.max_batch_size != new.max_batch_size ||
           old.replication_batch_max_age_secs != new.replication_batch_max_age_secs ||
           old.config_watch_interval_secs != new.config_watch_interval_secs {
            changes.push(ConfigChangeType::Performance);
        }

        changes
    }
}
